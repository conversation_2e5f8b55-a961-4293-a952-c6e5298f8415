<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hardware Components Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link rel="stylesheet" href="llm_modeling_metrics/web/static/css/styles.css">
</head>
<body>
    <div class="container mt-4">
        <h1>Hardware Components Test</h1>
        
        <!-- Hardware Selector Test -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Hardware Selector Component</h5>
                    </div>
                    <div class="card-body">
                        <div id="hardwareSelectorTest"></div>
                    </div>
                </div>
            </div>
            
            <!-- Roofline Visualizer Test -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Roofline Visualizer Component</h5>
                    </div>
                    <div class="card-body">
                        <div id="rooflineVisualizerTest" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <div class="text-muted">Running component tests...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    <script src="llm_modeling_metrics/web/static/js/hardware-selector.js"></script>
    <script src="llm_modeling_metrics/web/static/js/roofline-visualizer.js?v=3"></script>
    
    <script>
        // Test the components
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('testResults');
            const results = [];
            
            // Test Hardware Selector
            try {
                if (typeof HardwareSelector !== 'undefined') {
                    results.push('<div class="alert alert-success">✓ HardwareSelector class loaded successfully</div>');
                    
                    // Try to initialize (will fail without API, but class should exist)
                    try {
                        const selector = new HardwareSelector('hardwareSelectorTest', {
                            apiBaseUrl: '/api'
                        });
                        results.push('<div class="alert alert-success">✓ HardwareSelector can be instantiated</div>');
                    } catch (e) {
                        results.push('<div class="alert alert-warning">⚠ HardwareSelector instantiation failed (expected without API): ' + e.message + '</div>');
                    }
                } else {
                    results.push('<div class="alert alert-danger">✗ HardwareSelector class not found</div>');
                }
            } catch (e) {
                results.push('<div class="alert alert-danger">✗ Error testing HardwareSelector: ' + e.message + '</div>');
            }
            
            // Test Roofline Visualizer
            try {
                if (typeof RooflineVisualizer !== 'undefined') {
                    results.push('<div class="alert alert-success">✓ RooflineVisualizer class loaded successfully</div>');
                    
                    // Try to initialize
                    try {
                        const visualizer = new RooflineVisualizer('rooflineVisualizerTest', {
                            apiBaseUrl: '/api'
                        });
                        results.push('<div class="alert alert-success">✓ RooflineVisualizer can be instantiated</div>');
                    } catch (e) {
                        results.push('<div class="alert alert-warning">⚠ RooflineVisualizer instantiation failed: ' + e.message + '</div>');
                    }
                } else {
                    results.push('<div class="alert alert-danger">✗ RooflineVisualizer class not found</div>');
                }
            } catch (e) {
                results.push('<div class="alert alert-danger">✗ Error testing RooflineVisualizer: ' + e.message + '</div>');
            }
            
            // Test Chart.js availability
            if (typeof Chart !== 'undefined') {
                results.push('<div class="alert alert-success">✓ Chart.js loaded successfully</div>');
            } else {
                results.push('<div class="alert alert-danger">✗ Chart.js not found</div>');
            }
            
            // Test Select2 availability
            if (typeof $ !== 'undefined' && $.fn.select2) {
                results.push('<div class="alert alert-success">✓ Select2 loaded successfully</div>');
            } else {
                results.push('<div class="alert alert-warning">⚠ Select2 not available</div>');
            }
            
            // Display results
            testResults.innerHTML = results.join('');
        });
    </script>
</body>
</html>