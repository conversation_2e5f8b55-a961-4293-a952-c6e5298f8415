<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Roofline Controls - Log Scale & Wheel Zoom</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .chart-container {
            height: 500px;
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Roofline Controls Test</h1>
        <p class="text-muted">Testing the new log scale and wheel zoom controls for the Roofline Model visualization.</p>
        
        <div class="row">
            <div class="col-12">
                <!-- Test Controls -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-1"></i>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <button id="testLogScaleBtn" class="btn btn-primary">
                                    <i class="fas fa-chart-line me-1"></i>
                                    Toggle Log Scale
                                </button>
                                <span id="logScaleStatus" class="badge bg-success ms-2">Log Scale: ON</span>
                            </div>
                            <div class="col-md-6">
                                <button id="testWheelZoomBtn" class="btn btn-secondary">
                                    <i class="fas fa-mouse me-1"></i>
                                    Toggle Wheel Zoom
                                </button>
                                <span id="wheelZoomStatus" class="badge bg-success ms-2">Wheel Zoom: ON</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button id="initVisualizerBtn" class="btn btn-success">
                                <i class="fas fa-play me-1"></i>
                                Initialize Roofline Visualizer
                            </button>
                            <div id="status" class="mt-2"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Roofline Visualizer Container -->
                <div id="rooflineVisualizationContainer"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    <script src="llm_modeling_metrics/web/static/js/roofline-visualizer.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusDiv = document.getElementById('status');
            const logScaleStatus = document.getElementById('logScaleStatus');
            const wheelZoomStatus = document.getElementById('wheelZoomStatus');
            let visualizer = null;
            
            function updateStatus(message, type = 'info') {
                statusDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            }
            
            function updateLogScaleStatus(enabled) {
                logScaleStatus.textContent = `Log Scale: ${enabled ? 'ON' : 'OFF'}`;
                logScaleStatus.className = `badge ms-2 ${enabled ? 'bg-success' : 'bg-secondary'}`;
            }
            
            function updateWheelZoomStatus(enabled) {
                wheelZoomStatus.textContent = `Wheel Zoom: ${enabled ? 'ON' : 'OFF'}`;
                wheelZoomStatus.className = `badge ms-2 ${enabled ? 'bg-success' : 'bg-secondary'}`;
            }
            
            // Check dependencies
            if (typeof Chart === 'undefined') {
                updateStatus('Chart.js is not loaded!', 'danger');
                return;
            }
            
            if (typeof ChartZoom !== 'undefined') {
                Chart.register(ChartZoom.default || ChartZoom);
                updateStatus('Chart.js and zoom plugin loaded successfully', 'success');
            } else {
                updateStatus('Zoom plugin not found', 'warning');
            }
            
            if (typeof RooflineVisualizer === 'undefined') {
                updateStatus('RooflineVisualizer class not found! Make sure the script is loaded.', 'danger');
                return;
            }
            
            // Initialize Roofline Visualizer
            document.getElementById('initVisualizerBtn').addEventListener('click', function() {
                try {
                    visualizer = new RooflineVisualizer('rooflineVisualizationContainer', {
                        apiBaseUrl: '/api',
                        showControls: true,
                        showLegend: true,
                        enableZoom: true,
                        enablePan: true
                    });
                    
                    updateStatus('Roofline Visualizer initialized successfully! You can now test the controls.', 'success');
                    
                    // Add some test data
                    setTimeout(() => {
                        if (visualizer.chart) {
                            // Add sample roofline data
                            const sampleData = {
                                datasets: [
                                    {
                                        label: 'Test Roofline (FP16)',
                                        data: [
                                            {x: 0.1, y: 1},
                                            {x: 1, y: 10},
                                            {x: 10, y: 100},
                                            {x: 100, y: 312}
                                        ],
                                        borderColor: 'rgb(255, 99, 132)',
                                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                        type: 'line',
                                        datasetType: 'roofline',
                                        showLine: true,
                                        pointRadius: 0,
                                        borderWidth: 2
                                    },
                                    {
                                        label: 'Test Operators',
                                        data: [
                                            {x: 5, y: 50},
                                            {x: 15, y: 150},
                                            {x: 25, y: 200}
                                        ],
                                        backgroundColor: 'rgb(54, 162, 235)',
                                        borderColor: 'rgb(54, 162, 235)',
                                        type: 'scatter',
                                        datasetType: 'operator',
                                        pointRadius: 6
                                    }
                                ]
                            };
                            
                            visualizer.chart.data = sampleData;
                            visualizer.chart.update();
                            updateStatus('Sample data added to chart. Test the new controls!', 'info');
                        }
                    }, 1000);
                    
                } catch (error) {
                    updateStatus('Failed to initialize Roofline Visualizer: ' + error.message, 'danger');
                    console.error('Initialization error:', error);
                }
            });
            
            // Test log scale toggle
            document.getElementById('testLogScaleBtn').addEventListener('click', function() {
                if (visualizer) {
                    const logScaleCheckbox = document.getElementById('useLogScale');
                    if (logScaleCheckbox) {
                        logScaleCheckbox.checked = !logScaleCheckbox.checked;
                        logScaleCheckbox.dispatchEvent(new Event('change'));
                        updateLogScaleStatus(logScaleCheckbox.checked);
                        updateStatus(`Log scale ${logScaleCheckbox.checked ? 'enabled' : 'disabled'}`, 'info');
                    } else {
                        updateStatus('Log scale checkbox not found. Make sure the visualizer is initialized.', 'warning');
                    }
                } else {
                    updateStatus('Please initialize the visualizer first.', 'warning');
                }
            });
            
            // Test wheel zoom toggle
            document.getElementById('testWheelZoomBtn').addEventListener('click', function() {
                if (visualizer) {
                    const wheelZoomCheckbox = document.getElementById('enableWheelZoom');
                    if (wheelZoomCheckbox) {
                        wheelZoomCheckbox.checked = !wheelZoomCheckbox.checked;
                        wheelZoomCheckbox.dispatchEvent(new Event('change'));
                        updateWheelZoomStatus(wheelZoomCheckbox.checked);
                        updateStatus(`Wheel zoom ${wheelZoomCheckbox.checked ? 'enabled' : 'disabled'}`, 'info');
                    } else {
                        updateStatus('Wheel zoom checkbox not found. Make sure the visualizer is initialized.', 'warning');
                    }
                } else {
                    updateStatus('Please initialize the visualizer first.', 'warning');
                }
            });
        });
    </script>
</body>
</html>
