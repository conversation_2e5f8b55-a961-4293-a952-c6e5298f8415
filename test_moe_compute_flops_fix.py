#!/usr/bin/env python3
"""
Test script to verify that the MoE compute_flops method is fixed.
"""

from llm_modeling_metrics.models.moe_model import MoEModel

def test_moe_compute_flops():
    """Test the compute_flops method for MoE model."""
    
    print("Testing MoE compute_flops method...")
    
    config = {
        'hidden_size': 4096,
        'num_hidden_layers': 32,
        'num_attention_heads': 32,
        'num_key_value_heads': 8,
        'intermediate_size': 14336,
        'moe_intermediate_size': 1407,
        'n_shared_experts': 2,
        'n_routed_experts': 64,
        'num_experts_per_tok': 6,
        'num_dense_layers': 1,
        'num_moe_layers': 31,
        'vocab_size': 128256,
        'max_position_embeddings': 8192,
        'model_type': 'deepseek_v2',
        'tie_word_embeddings': False,
        'rms_norm_eps': 1e-05
    }
    
    model = MoEModel('test-moe-model', config)
    
    try:
        # Test the compute_flops method
        flops_breakdown = model.compute_flops(sequence_length=2048, batch_size=1)
        
        print("✅ MoE compute_flops works!")
        print("FLOPs breakdown:")
        for component, flops in flops_breakdown.items():
            print(f"  {component}: {flops:,}")
        
        total_flops = sum(flops_breakdown.values())
        print(f"\nTotal FLOPs: {total_flops:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ MoE compute_flops failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    
    print("=== Testing MoE compute_flops Fix ===\n")
    
    success = test_moe_compute_flops()
    
    print(f"\n=== Test Results ===")
    if success:
        print("✅ MoE compute_flops test passed!")
    else:
        print("❌ MoE compute_flops test failed.")
    
    return success

if __name__ == "__main__":
    main()
