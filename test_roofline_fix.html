<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Roofline Fix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .chart-container {
            height: 400px;
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Roofline Visualizer Test</h1>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Roofline Chart</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="testChart"></canvas>
                        </div>
                        <button id="testBtn" class="btn btn-primary mt-3">Test Chart Creation</button>
                        <div id="status" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusDiv = document.getElementById('status');
            const testBtn = document.getElementById('testBtn');
            
            function updateStatus(message, type = 'info') {
                statusDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            }
            
            // Test Chart.js availability
            if (typeof Chart === 'undefined') {
                updateStatus('Chart.js is not loaded!', 'danger');
                return;
            }
            
            updateStatus('Chart.js loaded successfully', 'success');
            
            // Test zoom plugin
            if (typeof ChartZoom !== 'undefined') {
                updateStatus('Chart.js and zoom plugin loaded successfully', 'success');
                try {
                    Chart.register(ChartZoom.default || ChartZoom);
                    updateStatus('Zoom plugin registered successfully', 'success');
                } catch (error) {
                    updateStatus('Failed to register zoom plugin: ' + error.message, 'warning');
                }
            } else {
                updateStatus('Zoom plugin not found', 'warning');
            }
            
            testBtn.addEventListener('click', function() {
                try {
                    const ctx = document.getElementById('testChart').getContext('2d');
                    
                    // Test creating a chart with mixed dataset types
                    const chart = new Chart(ctx, {
                        type: 'scatter',
                        data: {
                            datasets: [
                                {
                                    label: 'Roofline (FP16)',
                                    data: [
                                        {x: 0.1, y: 1},
                                        {x: 1, y: 10},
                                        {x: 10, y: 100},
                                        {x: 100, y: 312}
                                    ],
                                    borderColor: 'rgb(255, 99, 132)',
                                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                    type: 'line',
                                    datasetType: 'roofline',
                                    showLine: true,
                                    pointRadius: 0,
                                    borderWidth: 2
                                },
                                {
                                    label: 'Operators',
                                    data: [
                                        {x: 5, y: 50},
                                        {x: 15, y: 150},
                                        {x: 25, y: 200}
                                    ],
                                    backgroundColor: 'rgb(54, 162, 235)',
                                    borderColor: 'rgb(54, 162, 235)',
                                    type: 'scatter',
                                    datasetType: 'operator',
                                    pointRadius: 6
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: {
                                    type: 'logarithmic',
                                    title: {
                                        display: true,
                                        text: 'Operational Intensity (FLOP/Byte)'
                                    }
                                },
                                y: {
                                    type: 'logarithmic',
                                    title: {
                                        display: true,
                                        text: 'Performance (TFLOPS)'
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        title: (context) => {
                                            const point = context[0];
                                            if (point.dataset.datasetType === 'roofline') {
                                                return `${point.dataset.label} Roofline`;
                                            } else if (point.dataset.datasetType === 'operator') {
                                                return 'Operator Point';
                                            }
                                            return '';
                                        }
                                    }
                                }
                            }
                        }
                    });
                    
                    updateStatus('Chart created successfully! The roofline fix is working.', 'success');
                } catch (error) {
                    updateStatus('Failed to create chart: ' + error.message, 'danger');
                    console.error('Chart creation error:', error);
                }
            });
        });
    </script>
</body>
</html>
