<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Controls Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Interactive Controls Test</h1>
        
        <!-- Interactive Controls Container -->
        <div class="row">
            <div class="col-md-6">
                <div id="interactiveControlsContainer"></div>
            </div>
            <div class="col-md-6">
                <div id="advancedVisualizationControlsContainer"></div>
            </div>
        </div>
        
        <!-- Test Output -->
        <div class="mt-4">
            <h3>Test Output</h3>
            <pre id="testOutput" class="bg-light p-3"></pre>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="llm_modeling_metrics/web/static/js/interactive-controls.js"></script>
    <script src="llm_modeling_metrics/web/static/js/advanced-visualization-controls.js"></script>
    
    <script>
        // Test the components
        document.addEventListener('DOMContentLoaded', function() {
            const output = document.getElementById('testOutput');
            
            try {
                // Test Interactive Controls
                const interactiveControls = new InteractiveControls('interactiveControlsContainer', {
                    onParameterChange: (parameters) => {
                        output.textContent += `Interactive Parameters Changed: ${JSON.stringify(parameters, null, 2)}\n`;
                    },
                    onPrecisionChange: (precisionConfig) => {
                        output.textContent += `Precision Changed: ${JSON.stringify(precisionConfig, null, 2)}\n`;
                    },
                    onPresetLoad: (presetKey, preset) => {
                        output.textContent += `Preset Loaded: ${presetKey} - ${preset.name}\n`;
                    }
                });
                
                // Test Advanced Visualization Controls
                const advancedControls = new AdvancedVisualizationControls('advancedVisualizationControlsContainer', {
                    onFilterChange: (filters) => {
                        output.textContent += `Filters Changed: ${JSON.stringify(filters, null, 2)}\n`;
                    },
                    onExport: async (type, format) => {
                        output.textContent += `Export Requested: ${type} as ${format}\n`;
                        return Promise.resolve();
                    }
                });
                
                output.textContent = 'Interactive Controls initialized successfully!\n';
                output.textContent += `Available presets: ${interactiveControls.getAvailablePresets().join(', ')}\n`;
                
            } catch (error) {
                output.textContent = `Error: ${error.message}\n${error.stack}`;
            }
        });
    </script>
</body>
</html>